{"name": "project-w", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "sh scripts/copyConfig.sh && npm ci", "venue:index": "CITY_KEY='delhi' node scripts/createVenueIndex.js", "event:index": "CITY_KEY='delhi' node scripts/createEventIndex.js", "create-super-admin": "node scripts/createSuperAdmin.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/s3-request-presigner": "^3.577.0", "@elastic/elasticsearch": "^8.13.1", "ajv": "^8.12.0", "axios": "^1.6.8", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "firebase-admin": "^12.1.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.3.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "razorpay": "^2.9.4"}, "devDependencies": {"nodemon": "^3.1.0"}}